// Monitor Filter 相关类型定义

export interface FilterField {
  key: string
  label: string
  type: 'input' | 'select'
  placeholder?: string
  options?: Array<{ value: string, label: string }>
  width?: string
}

export interface BaseFilterConfig {
  timeRange: string
  customStartTime: string
  customEndTime: string
}

// 错误监控筛选配置
export interface ErrorFilterConfig extends BaseFilterConfig {
  eventId: string
  userUuid: string
  pageUrl: string
}

// 性能监控筛选配置
export interface PerformanceFilterConfig extends BaseFilterConfig {
  metricType: string
  pageUrl: string
  userUuid: string
}

// 页面访问监控筛选配置
export interface PageViewFilterConfig extends BaseFilterConfig {
  pageUrl: string
  userUuid: string
  visitType: string
}

// 会话监控筛选配置
export interface SessionFilterConfig extends BaseFilterConfig {
  userUuid: string
  platform: string
  deviceType: string
}

// 通用筛选配置类型
export type FilterConfig = BaseFilterConfig & Record<string, any>

// 时间范围数据
export interface TimeRangeData {
  startTime: number
  endTime: number
}

// 筛选字段配置预设
export const FILTER_FIELD_PRESETS = {
  // 错误监控字段
  ERROR_FIELDS: [
    {
      key: 'eventId',
      label: '错误类型',
      type: 'select' as const,
      placeholder: '选择错误类型',
      options: [
        { value: 'script.error', label: '脚本错误' },
        { value: 'resource.error', label: '资源加载错误' },
        { value: 'promise.error', label: 'Promise错误' },
        { value: 'network.error', label: '网络错误' },
        { value: 'custom.error', label: '自定义错误' },
      ],
    },
    {
      key: 'userUuid',
      label: '用户ID',
      type: 'input' as const,
      placeholder: '用户UUID',
    },
    {
      key: 'pageUrl',
      label: '页面URL',
      type: 'input' as const,
      placeholder: '页面地址',
    },
  ] as FilterField[],

  // 性能监控字段
  PERFORMANCE_FIELDS: [
    {
      key: 'metricType',
      label: '性能指标',
      type: 'select' as const,
      placeholder: '选择性能指标',
      options: [
        { value: 'paint', label: '首次绘制时间' },
        { value: 'navigation', label: '页面导航时间' },
        { value: 'resource', label: '资源加载时间' },
        { value: 'web-vitals', label: 'Web Vitals' },
      ],
    },
    {
      key: 'pageUrl',
      label: '页面URL',
      type: 'input' as const,
      placeholder: '页面地址',
    },
    {
      key: 'userUuid',
      label: '用户ID',
      type: 'input' as const,
      placeholder: '用户UUID',
    },
  ] as FilterField[],

  // 页面访问监控字段
  PAGE_VIEW_FIELDS: [
    {
      key: 'pageUrl',
      label: '页面URL',
      type: 'input' as const,
      placeholder: '页面地址',
    },
    {
      key: 'userUuid',
      label: '用户ID',
      type: 'input' as const,
      placeholder: '用户UUID',
    },
    {
      key: 'visitType',
      label: '访问类型',
      type: 'select' as const,
      placeholder: '选择访问类型',
      options: [
        { value: 'first', label: '首次访问' },
        { value: 'return', label: '回访' },
        { value: 'direct', label: '直接访问' },
        { value: 'referral', label: '外链访问' },
      ],
    },
  ] as FilterField[],

  // 会话监控字段
  SESSION_FIELDS: [
    {
      key: 'userUuid',
      label: '用户ID',
      type: 'input' as const,
      placeholder: '用户UUID',
    },
    {
      key: 'platform',
      label: '平台',
      type: 'select' as const,
      placeholder: '选择平台',
      options: [
        { value: 'web', label: 'Web' },
        { value: 'mobile', label: '移动端' },
        { value: 'desktop', label: '桌面端' },
      ],
    },
    {
      key: 'deviceType',
      label: '设备类型',
      type: 'select' as const,
      placeholder: '选择设备类型',
      options: [
        { value: 'desktop', label: '桌面设备' },
        { value: 'mobile', label: '移动设备' },
        { value: 'tablet', label: '平板设备' },
      ],
    },
  ] as FilterField[],
}

// 默认筛选配置
export const DEFAULT_FILTER_CONFIG: BaseFilterConfig = {
  timeRange: '24h',
  customStartTime: '',
  customEndTime: '',
}

// 创建默认筛选配置的工厂函数
export function createDefaultFilterConfig<T extends Record<string, any>>(
  additionalFields: (keyof T)[]
): BaseFilterConfig & T {
  const config = { ...DEFAULT_FILTER_CONFIG } as BaseFilterConfig & T
  
  additionalFields.forEach(field => {
    ;(config as any)[field] = ''
  })
  
  return config
}
