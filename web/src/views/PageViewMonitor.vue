<script setup lang="ts">
import type { MonitorEvent } from '@/api/monitor'
import { Eye, Filter, RefreshCw } from 'lucide-vue-next'
import { computed, h, onMounted, reactive, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import DataCard from '@/components/monitor/DataCard.vue'
import DataTable from '@/components/monitor/DataTable.vue'
import MonitorHeader from '@/components/monitor/MonitorHeader.vue'
import PageViewDetailDialog from '@/components/monitor/PageViewDetailDialog.vue'
import ResponsiveContainer from '@/components/monitor/ResponsiveContainer.vue'
import ResponsiveGrid from '@/components/monitor/ResponsiveGrid.vue'
import TimeRangeSelector from '@/components/monitor/TimeRangeSelector.vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useMonitorStore } from '@/store/monitor'
import { useProjectStore } from '@/store/project'
import {
  formatDuration,
  formatTimestamp,
  formatUrl,
  getDeviceInfo,
  getPageTitle,
} from '@/utils/eventDataMapper'

const router = useRouter()
const route = useRoute()
const projectStore = useProjectStore()
const monitorStore = useMonitorStore()

// 状态
const loading = ref(false)
const showVisitDetail = ref(false)
const selectedVisit = ref<MonitorEvent | null>(null)
const currentPage = ref(1)

// 筛选条件
const filters = reactive({
  timeRange: '24h',
  pageUrl: '',
  userUuid: '',
  visitType: '',
  customStartTime: '',
  customEndTime: '',
})

// 计算属性
const currentProject = computed(() => projectStore.currentProject)
const pageViews = computed(() => monitorStore.pageViews)
const pagination = computed(() => monitorStore.pagination)
const totalPages = computed(() => monitorStore.totalPages)

// 页面访问统计
const pageViewStats = computed(() => {
  const data = pageViews.value

  // 基础统计
  const totalViews = pagination.value.total
  const uniqueVisitors = new Set(data.map(v => v.user_uuid)).size
  const uniquePages = new Set(data.map(v => v.trigger_page_url)).size

  // 计算平均停留时长（如果有duration数据）
  const durationsWithData = data.filter(v => v.event_data?.duration).map(v => v.event_data.duration)
  const averageDuration = durationsWithData.length > 0
    ? Math.round(durationsWithData.reduce((a, b) => a + b, 0) / durationsWithData.length)
    : 0

  // 热门页面统计
  const pageStats = data.reduce((acc, visit) => {
    const url = visit.trigger_page_url
    const title = getPageTitle(visit.event_data) || formatUrl(url).display

    if (!acc[url]) {
      acc[url] = { url, title, count: 0 }
    }
    acc[url].count++
    return acc
  }, {} as Record<string, { url: string, title: string, count: number }>)

  const popularPages = Object.values(pageStats)
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)

  // 设备类型统计
  const deviceStats = data.reduce((acc, visit) => {
    const deviceInfo = getDeviceInfo(visit.event_data)
    acc[deviceInfo.deviceType] = (acc[deviceInfo.deviceType] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return {
    totalViews,
    uniqueVisitors,
    uniquePages,
    averageDuration,
    popularPages,
    deviceStats,
  }
})

// 表格列配置
const tableColumns = computed(() => [
  {
    key: 'trigger_page_url',
    title: '页面',
    sortable: true,
    render: (value: string, record: MonitorEvent) => {
      const urlInfo = formatUrl(value, 50)
      const title = getPageTitle(record.event_data)
      return h('div', { class: 'space-y-1' }, [
        h('div', {
          class: 'font-medium truncate',
          title: title || urlInfo.full,
        }, title || urlInfo.display),
        h('div', {
          class: 'text-xs text-muted-foreground truncate',
          title: urlInfo.full,
        }, urlInfo.domain),
      ])
    },
  },
  {
    key: 'event_data',
    title: '访问信息',
    render: (value: any, record: MonitorEvent) => {
      const deviceInfo = getDeviceInfo(value)
      const duration = value?.duration
      return h('div', { class: 'text-xs space-y-1' }, [
        h('div', `${deviceInfo.platform} / ${deviceInfo.browser}`),
        h('div', `${deviceInfo.deviceType} (${deviceInfo.screenSize})`),
        duration ? h('div', `停留: ${formatDuration(duration)}`) : null,
      ].filter(Boolean))
    },
  },
  {
    key: 'user_uuid',
    title: '用户',
    render: (value: string) => {
      return h('code', { class: 'text-xs' }, `${value?.slice(0, 8)}...` || '未知')
    },
  },
  {
    key: 'trigger_time',
    title: '访问时间',
    sortable: true,
    render: (value: number) => {
      return formatTimestamp(value, 'relative')
    },
  },
])

// 表格行操作
const tableActions = [
  {
    label: '查看详情',
    action: (record: MonitorEvent) => {
      selectedVisit.value = record
      showVisitDetail.value = true
    },
  },
]

// 时间范围变化处理
function handleTimeRangeChange(timeRangeData: { startTime: number, endTime: number }) {
  currentPage.value = 1
  loadData(timeRangeData.startTime, timeRangeData.endTime)
}

// 加载数据
async function loadData(startTime?: number, endTime?: number) {
  if (!currentProject.value)
    return

  loading.value = true
  try {
    // 如果没有传入时间参数，使用默认的24小时
    const now = Date.now()
    const defaultStartTime = startTime || (now - 24 * 60 * 60 * 1000)
    const defaultEndTime = endTime || now

    const params: any = {
      project_id: currentProject.value.id,
      page: currentPage.value,
      page_size: 20,
      start_time: defaultStartTime,
      end_time: defaultEndTime,
    }

    if (filters.pageUrl)
      params.page_url = filters.pageUrl
    if (filters.userUuid)
      params.user_uuid = filters.userUuid
    if (filters.visitType)
      params.visit_type = filters.visitType

    await monitorStore.fetchPageViews(params)
  }
  catch (error) {
    console.error('加载页面访问数据失败:', error)
    toast.error('加载页面访问数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  currentPage.value = 1
  loadData()
}

// 重置筛选条件
function resetFilters() {
  filters.timeRange = '24h'
  filters.pageUrl = ''
  filters.userUuid = ''
  filters.visitType = ''
  filters.customStartTime = ''
  filters.customEndTime = ''
  currentPage.value = 1
  loadData()
}

// 筛选条件变化处理
function handleFilterChange() {
  currentPage.value = 1
  loadData()
}

// 分页处理
function handlePageChange(page: number) {
  currentPage.value = page
  loadData()
}

// 表格行点击处理
function handleRowClick(record: MonitorEvent) {
  selectedVisit.value = record
  showVisitDetail.value = true
}

// 监听项目切换
watch(() => projectStore.currentProject, (newProject, oldProject) => {
  // 只有当项目真正发生变化时才刷新数据
  if (newProject && oldProject && newProject.id !== oldProject.id) {
    loadData()
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  // 设置默认的自定义时间（最近24小时）
  const now = new Date()
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

  filters.customEndTime = now.toISOString().slice(0, 16)
  filters.customStartTime = yesterday.toISOString().slice(0, 16)
})
</script>

<template>
  <ResponsiveContainer type="page" max-width="1400px" centered>
    <!-- 页头 -->
    <MonitorHeader
      title="页面访问监控"
      description="监控和分析页面访问情况"
      :loading="loading"
      :on-refresh="refreshData"
    />

    <!-- 时间范围和筛选条件 -->
    <Card class="mb-6">
      <CardHeader>
        <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <CardTitle class="text-lg flex items-center gap-2">
            <Filter class="w-5 h-5" />
            筛选条件
          </CardTitle>
          <TimeRangeSelector
            v-model="filters.timeRange"
            v-model:custom-start-time="filters.customStartTime"
            v-model:custom-end-time="filters.customEndTime"
            :disabled="loading"
            @change="handleTimeRangeChange"
          />
        </div>
      </CardHeader>
      <CardContent>
        <div class="flex flex-wrap gap-8">
          <div class="space-y-2">
            <Label>页面URL</Label>
            <Input
              v-model="filters.pageUrl"
              placeholder="页面地址"
              @keyup.enter="handleFilterChange"
            />
          </div>
          <div class="space-y-2">
            <Label>用户ID</Label>
            <Input
              v-model="filters.userUuid"
              placeholder="用户UUID"
              @keyup.enter="handleFilterChange"
            />
          </div>
          <div class="space-y-2">
            <Label>访问类型</Label>
            <Select v-model="filters.visitType" @update:model-value="handleFilterChange">
              <SelectTrigger>
                <SelectValue placeholder="选择访问类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="first">
                  首次访问
                </SelectItem>
                <SelectItem value="return">
                  返回访问
                </SelectItem>
                <SelectItem value="reload">
                  页面刷新
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div class="flex justify-end mt-4">
          <Button variant="outline" @click="resetFilters">
            <RefreshCw class="w-4 h-4 mr-2" />
            重置筛选
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- 统计卡片 -->
    <ResponsiveGrid
      :cols="{ xs: 1, sm: 2, md: 2, lg: 4 }"
      gap="md"
      class="mb-6"
    >
      <DataCard
        title="总访问量"
        description="页面访问总数"
        :value="pageViewStats.totalViews"
        status="info"
        :loading="loading"
      />

      <DataCard
        title="独立访客"
        description="唯一访问用户数"
        :value="pageViewStats.uniqueVisitors"
        status="success"
        :loading="loading"
      />

      <DataCard
        title="访问页面数"
        description="被访问的页面数量"
        :value="pageViewStats.uniquePages"
        status="warning"
        :loading="loading"
      />

      <DataCard
        title="平均停留时长"
        description="用户平均停留时间"
        :value="pageViewStats.averageDuration > 0 ? formatDuration(pageViewStats.averageDuration) : '暂无数据'"
        status="info"
        :loading="loading"
      />
    </ResponsiveGrid>

    <!-- 热门页面排行 -->
    <ResponsiveGrid
      :cols="{ xs: 1, lg: 2 }"
      gap="lg"
      class="mb-6"
    >
      <Card>
        <CardHeader>
          <CardTitle class="text-lg">
            热门页面
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div v-if="pageViewStats.popularPages.length > 0" class="space-y-3">
            <div
              v-for="(page, index) in pageViewStats.popularPages"
              :key="page.url"
              class="flex items-center justify-between p-3 bg-muted rounded-lg"
            >
              <div class="flex-1">
                <div class="flex items-center gap-2">
                  <Badge variant="outline" class="text-xs">
                    {{ index + 1 }}
                  </Badge>
                  <span class="text-sm font-medium truncate">{{ page.title }}</span>
                </div>
                <p class="text-xs text-muted-foreground mt-1 truncate">
                  {{ page.url }}
                </p>
              </div>
              <div class="text-right">
                <div class="text-lg font-bold">
                  {{ page.count }}
                </div>
                <div class="text-xs text-muted-foreground">
                  访问次数
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8 text-muted-foreground">
            <Eye class="w-12 h-12 mx-auto mb-2" />
            <p>暂无热门页面数据</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle class="text-lg">
            设备类型分布
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div v-if="Object.keys(pageViewStats.deviceStats).length > 0" class="space-y-3">
            <div
              v-for="(count, deviceType) in pageViewStats.deviceStats"
              :key="deviceType"
              class="flex items-center justify-between p-3 bg-muted rounded-lg"
            >
              <div class="flex items-center gap-2">
                <span class="text-sm font-medium capitalize">{{ deviceType }}</span>
              </div>
              <div class="text-right">
                <div class="text-lg font-bold">
                  {{ count }}
                </div>
                <div class="text-xs text-muted-foreground">
                  {{ ((count / pageViewStats.totalViews) * 100).toFixed(1) }}%
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8 text-muted-foreground">
            <Eye class="w-12 h-12 mx-auto mb-2" />
            <p>暂无设备数据</p>
          </div>
        </CardContent>
      </Card>
    </ResponsiveGrid>

    <!-- 访问记录列表 -->
    <Card>
      <CardHeader>
        <div class="flex justify-between items-center">
          <CardTitle>访问记录</CardTitle>
          <div class="flex items-center gap-2">
            <span class="text-sm text-muted-foreground">
              共 {{ pagination.total }} 条记录
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <DataTable
          :data="pageViews"
          :columns="tableColumns"
          :loading="loading"
          :row-actions="tableActions"
          searchable
          search-placeholder="搜索页面标题、URL..."
          empty-text="暂无页面访问记录"
          @row-click="handleRowClick"
        />
      </CardContent>
    </Card>

    <!-- 分页 -->
    <div v-if="totalPages > 1" class="flex justify-center mt-6">
      <Pagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @page-change="handlePageChange"
      />
    </div>

    <!-- 访问详情对话框 -->
    <PageViewDetailDialog
      v-model:open="showVisitDetail"
      :event="selectedVisit"
    />
  </ResponsiveContainer>
</template>

<style scoped>
.page-view-monitor {
  padding: 1.5rem;
}

@media (min-width: 768px) {
  .page-view-monitor {
    padding: 2rem;
  }
}
</style>
