import type { RouteRecordRaw } from 'vue-router'
import { FolderOpen, TestTube } from 'lucide-vue-next'
import { createRouter, createWebHistory } from 'vue-router'
import { useProjectStore } from '@/store'
import { useUserStore } from '@/store/user'

export const menuRoutes: RouteRecordRaw[] = [
  {
    path: '/projects',
    name: 'Projects',
    component: () => import('@/views/ProjectList.vue'),
    meta: {
      title: '项目管理',
      icon: FolderOpen,
    },
  },
  {
    path: '/playground',
    name: 'Playground',
    component: () => import('@/views/Playground.vue'),
    meta: {
      title: '测试场地',
      icon: TestTube,
    },
  },
  {
    path: '/monitor/:id',
    name: 'MonitorDashboard',
    component: () => import('@/views/MonitorDashboard.vue'),
    beforeEnter: setProjectId,
    meta: {
      title: '监控面板',
      requiresProject: true,
    },
  },
  {
    path: '/monitor/:id/errors',
    name: 'ErrorMonitor',
    component: () => import('@/views/ErrorMonitor.vue'),
    beforeEnter: setProjectId,
    meta: {
      title: '错误监控',
      requiresProject: true,
    },
  },
  {
    path: '/monitor/:id/page-views',
    name: 'PageViewMonitor',
    component: () => import('@/views/PageViewMonitor.vue'),
    beforeEnter: setProjectId,
    meta: {
      title: '页面访问监控',
      requiresProject: true,
    },
  },
  {
    path: '/monitor/:id/performance',
    name: 'PerformanceMonitor',
    component: () => import('@/views/PerformanceMonitor.vue'),
    beforeEnter: setProjectId,
    meta: {
      title: '性能监控',
      requiresProject: true,
    },
  },
  {
    path: '/monitor/:id/sessions',
    name: 'SessionMonitor',
    component: () => import('@/views/SessionMonitor.vue'),
    beforeEnter: setProjectId,
    meta: {
      title: '用户会话监控',
      requiresProject: true,
    },
  },
]

export const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: {
        requiresAuth: false,
      },
    },
    {
      path: '/',
      component: () => import('@/layouts/AppLayout.vue'),
      meta: {
        requiresAuth: true,
      },
      children: [
        {
          path: '',
          redirect: '/projects',
        },
        ...menuRoutes,
      ],
    },
  ],
})

// 路由守卫
router.beforeEach((to, _, next) => {
  const userStore = useUserStore()

  if (to.meta.requiresAuth !== false && !userStore.isLogin) {
    // 需要登录但未登录，跳转到登录页
    next({ name: 'Login', query: { redirect: to.fullPath } })
  }
  else if (to.name === 'Login' && userStore.isLogin) {
    // 已登录但访问登录页，跳转到首页
    next({ path: (to.query.redirect as string) || '/' })
  }
  else {
    next()
  }
})

function setProjectId(to, _, next) {
  const projectStore = useProjectStore()
  if (to.params.id === ':id') {
    to.params.id = projectStore.currentProject?.id || ''
    next({
      name: to.name,
      params: to.params,
    })
  }
  else {
    next()
  }
}
