<script setup lang="ts">
import { Filter, RefreshCw } from 'lucide-vue-next'
import { reactive, watch } from 'vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import TimeRangeSelector from './TimeRangeSelector.vue'

export interface FilterField {
  key: string
  label: string
  type: 'input' | 'select'
  placeholder?: string
  options?: Array<{ value: string, label: string }>
  width?: string
}

export interface FilterConfig {
  timeRange: string
  customStartTime: string
  customEndTime: string
  [key: string]: any
}

interface Props {
  title?: string
  loading?: boolean
  fields?: FilterField[]
  modelValue: FilterConfig
}

const props = withDefaults(defineProps<Props>(), {
  title: '筛选条件',
  loading: false,
  fields: () => [],
})

const emit = defineEmits<{
  'update:modelValue': [value: FilterConfig]
  'filter-change': []
  'time-range-change': [timeRange: { startTime: number, endTime: number }]
  'reset': []
}>()

// 本地筛选条件
const localFilters = reactive<FilterConfig>({ ...props.modelValue })

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(localFilters, newValue)
}, { deep: true })

// 监听本地筛选条件变化
watch(localFilters, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })

// 处理时间范围变化
function handleTimeRangeChange(timeRange: { startTime: number, endTime: number }) {
  emit('time-range-change', timeRange)
}

// 处理筛选条件变化
function handleFilterChange() {
  emit('filter-change')
}

// 重置筛选条件
function resetFilters() {
  // 重置为默认值
  const defaultFilters: FilterConfig = {
    timeRange: '24h',
    customStartTime: '',
    customEndTime: '',
  }
  
  // 重置其他字段为空值
  props.fields.forEach(field => {
    defaultFilters[field.key] = ''
  })
  
  Object.assign(localFilters, defaultFilters)
  emit('reset')
}

// 处理输入框回车事件
function handleInputEnter() {
  handleFilterChange()
}
</script>

<template>
  <Card class="mb-6">
    <CardHeader>
      <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <CardTitle class="text-lg flex items-center gap-2">
          <Filter class="w-5 h-5" />
          {{ title }}
        </CardTitle>
        <TimeRangeSelector
          v-model="localFilters.timeRange"
          v-model:custom-start-time="localFilters.customStartTime"
          v-model:custom-end-time="localFilters.customEndTime"
          :disabled="loading"
          @change="handleTimeRangeChange"
        />
      </div>
    </CardHeader>
    <CardContent>
      <div class="flex flex-wrap gap-8">
        <!-- 动态渲染筛选字段 -->
        <div
          v-for="field in fields"
          :key="field.key"
          class="space-y-2"
          :style="{ width: field.width || 'auto' }"
        >
          <Label>{{ field.label }}</Label>
          
          <!-- 输入框类型 -->
          <Input
            v-if="field.type === 'input'"
            v-model="localFilters[field.key]"
            :placeholder="field.placeholder"
            @keyup.enter="handleInputEnter"
          />
          
          <!-- 选择框类型 -->
          <Select
            v-else-if="field.type === 'select'"
            v-model="localFilters[field.key]"
            @update:model-value="handleFilterChange"
          >
            <SelectTrigger>
              <SelectValue :placeholder="field.placeholder" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="option in field.options"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <!-- 重置按钮 -->
      <div class="flex justify-end mt-4">
        <Button variant="outline" @click="resetFilters">
          <RefreshCw class="w-4 h-4 mr-2" />
          重置筛选
        </Button>
      </div>
    </CardContent>
  </Card>
</template>
