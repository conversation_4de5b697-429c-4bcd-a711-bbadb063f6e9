<script setup lang="ts">
import { ArrowLeft, RefreshCw } from 'lucide-vue-next'
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import ProjectSelector from './ProjectSelector.vue'

interface Props {
  title: string
  description?: string
  showBackButton?: boolean
  backTo?: string
  loading?: boolean
  onRefresh?: () => void | Promise<void>
}

const props = withDefaults(defineProps<Props>(), {
  showBackButton: true,
  backTo: '/projects',
  loading: false,
})

const emit = defineEmits<{
  refresh: []
}>()

const router = useRouter()

// 处理返回按钮点击
function handleBack() {
  if (props.backTo) {
    router.push(props.backTo)
  }
  else {
    router.back()
  }
}

// 处理刷新按钮点击
async function handleRefresh() {
  if (props.onRefresh) {
    await props.onRefresh()
  }
  emit('refresh')
}

// 计算是否显示刷新按钮
const showRefreshButton = computed(() => {
  return props.onRefresh
})
</script>

<template>
  <Card class="mb-6">
    <CardContent>
      <div class="header flex items-center justify-between">
        <!-- 左侧：标题和描述 -->
        <div>
          <h1 class="text-2xl font-bold text-foreground">
            {{ title }}
          </h1>
          <p
            v-if="description"
            class="text-sm text-muted-foreground mt-1"
          >
            {{ description }}
          </p>
        </div>

        <!-- 右侧：项目选择器和操作按钮 -->
        <div class="project-select flex items-center space-x-2">
          <!-- 项目选择器 -->
          <ProjectSelector />

          <!-- 分隔线 -->
          <Separator orientation="vertical" class="h-6" />

          <!-- 刷新按钮 -->
          <Button
            v-if="showRefreshButton"
            variant="outline"
            size="sm"
            :disabled="loading"
            @click="handleRefresh"
          >
            <RefreshCw
              class="h-4 w-4 mr-2"
              :class="{ 'animate-spin': loading }"
            />
            刷新
          </Button>

          <!-- 额外的操作按钮插槽 -->
          <slot name="actions" />
        </div>
      </div>

      <!-- 额外内容插槽 -->
      <div v-if="$slots.default" class="mt-4">
        <Separator class="mb-4" />
        <slot />
      </div>
    </CardContent>
  </Card>
</template>

<style scoped>
@reference '../../style.css';

/* 确保在小屏幕上的响应式布局 */
@media (max-width: 768px) {
  .header {
    @apply flex-col items-start space-y-4;
  }

  .project-select:last-child {
    @apply w-full justify-between;
  }
}
</style>
