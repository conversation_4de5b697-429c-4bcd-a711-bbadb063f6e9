<script setup lang="ts">
import { ChevronDown, FolderOpen } from 'lucide-vue-next'
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useProjectStore } from '@/store/project'

const router = useRouter()
const projectStore = useProjectStore()

// 状态
const loading = ref(false)

// 计算属性
const projects = computed(() => projectStore.projects)
const currentProject = computed(() => projectStore.currentProject)
const hasProjects = computed(() => projectStore.hasProjects)

// 当前选择的项目ID（用于Select组件）
const selectedProjectId = computed({
  get: () => currentProject.value?.id || '',
  set: (value: string) => {
    if (value && value !== currentProject.value?.id) {
      handleProjectChange(value)
    }
  },
})

// 处理项目切换
async function handleProjectChange(projectId: string) {
  if (!projectId)
    return

  loading.value = true
  try {
    await projectStore.setCurrentProjectById(projectId)

    // 如果当前在监控页面，需要更新路由
    const currentRoute = router.currentRoute.value
    if (currentRoute.path.startsWith('/monitor/')) {
      // 提取当前的监控页面类型
      const pathParts = currentRoute.path.split('/')
      if (pathParts.length >= 4) {
        // 路径格式: /monitor/:id/:type
        const monitorType = pathParts.slice(3).join('/')
        const newPath = `/monitor/${projectId}/${monitorType}`
        await router.push(newPath)
      }
      else {
        // 只是监控面板首页
        const newPath = `/monitor/${projectId}`
        await router.push(newPath)
      }
    }

    toast.success(`已切换到项目: ${projectStore.currentProject?.name}`)
  }
  catch (error) {
    console.error('切换项目失败:', error)
    toast.error('切换项目失败')
  }
  finally {
    loading.value = false
  }
}

// 加载项目列表
async function loadProjects() {
  if (hasProjects.value)
    return

  loading.value = true
  try {
    await projectStore.fetchProjects()
  }
  catch (error) {
    console.error('加载项目列表失败:', error)
    toast.error('加载项目列表失败')
  }
  finally {
    loading.value = false
  }
}

// 组件挂载时加载项目列表
onMounted(async () => {
  await loadProjects()
})
</script>

<template>
  <div class="flex items-center space-x-2">
    <Select
      v-model="selectedProjectId"
      :disabled="loading || !hasProjects"
    >
      <SelectTrigger class="w-[200px]">
        <SelectValue
          :placeholder="loading ? '加载中...' : hasProjects ? '选择项目' : '暂无项目'"
        />
      </SelectTrigger>
      <SelectContent>
        <SelectItem
          v-for="project in projects"
          :key="project.id"
          :value="project.id"
        >
          <div class="flex flex-col">
            <span class="font-medium">{{ project.name }}</span>
            <span class="text-xs text-muted-foreground">{{ project.app_name || project.app_code }}</span>
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  </div>
</template>
