<script setup lang="ts">
import { Calendar, Clock } from 'lucide-vue-next'
import { computed, ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import { Calendar as CalendarComponent } from '@/components/ui/calendar'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { formatTimestamp } from '@/utils/eventDataMapper'

interface TimeRange {
  startTime: number
  endTime: number
}

interface Props {
  modelValue: string
  customStartTime?: string
  customEndTime?: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'update:customStartTime': [value: string]
  'update:customEndTime': [value: string]
  'change': [timeRange: TimeRange]
}>()

const localValue = ref(props.modelValue)
const localStartTime = ref(props.customStartTime || '')
const localEndTime = ref(props.customEndTime || '')
const showCustomPicker = ref(false)

const timeRangeOptions = [
  { value: '1h', label: '最近1小时' },
  { value: '6h', label: '最近6小时' },
  { value: '24h', label: '最近24小时' },
  { value: '3d', label: '最近3天' },
  { value: '7d', label: '最近7天' },
  { value: '30d', label: '最近30天' },
  { value: 'custom', label: '自定义时间' },
]

const currentTimeRange = computed(() => {
  return getTimeRange(localValue.value)
})

const isCustomRange = computed(() => {
  return localValue.value === 'custom'
})

const customRangeValid = computed(() => {
  if (!isCustomRange.value)
    return true

  if (!localStartTime.value || !localEndTime.value)
    return false

  const start = new Date(localStartTime.value)
  const end = new Date(localEndTime.value)

  return start.getTime() < end.getTime()
})

function getTimeRange(range: string): TimeRange {
  const now = Date.now()
  let startTime: number
  let endTime = now

  switch (range) {
    case '1h':
      startTime = now - 60 * 60 * 1000
      break
    case '6h':
      startTime = now - 6 * 60 * 60 * 1000
      break
    case '24h':
      startTime = now - 24 * 60 * 60 * 1000
      break
    case '3d':
      startTime = now - 3 * 24 * 60 * 60 * 1000
      break
    case '7d':
      startTime = now - 7 * 24 * 60 * 60 * 1000
      break
    case '30d':
      startTime = now - 30 * 24 * 60 * 60 * 1000
      break
    case 'custom':
      if (localStartTime.value && localEndTime.value) {
        startTime = new Date(localStartTime.value).getTime()
        endTime = new Date(localEndTime.value).getTime()
      }
      else {
        startTime = now - 24 * 60 * 60 * 1000
      }
      break
    default:
      startTime = now - 24 * 60 * 60 * 1000
  }

  return { startTime, endTime }
}

function handleRangeChange(value: string) {
  localValue.value = value
  emit('update:modelValue', value)

  if (value !== 'custom') {
    emitTimeRange()
  }
}

function handleCustomTimeChange() {
  emit('update:customStartTime', localStartTime.value)
  emit('update:customEndTime', localEndTime.value)
}

function applyCustomRange() {
  if (customRangeValid.value) {
    emitTimeRange()
    showCustomPicker.value = false
  }
}

function emitTimeRange() {
  const timeRange = getTimeRange(localValue.value)
  emit('change', timeRange)
}

function setQuickRange(hours: number) {
  const now = new Date()
  const start = new Date(now.getTime() - hours * 60 * 60 * 1000)

  localStartTime.value = start.toISOString().slice(0, 16)
  localEndTime.value = now.toISOString().slice(0, 16)

  handleCustomTimeChange()
}

// 初始化默认的自定义时间
function initializeCustomTime() {
  if (!localStartTime.value || !localEndTime.value) {
    const now = new Date()
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

    localStartTime.value = yesterday.toISOString().slice(0, 16)
    localEndTime.value = now.toISOString().slice(0, 16)
  }
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  localValue.value = newValue
})

watch(() => props.customStartTime, (newValue) => {
  if (newValue)
    localStartTime.value = newValue
})

watch(() => props.customEndTime, (newValue) => {
  if (newValue)
    localEndTime.value = newValue
})

// 初始化
initializeCustomTime()
</script>

<template>
  <div class="flex items-center gap-2">
    <Label class="text-sm font-medium">时间范围:</Label>

    <Select
      :model-value="localValue"
      :disabled="disabled"
      @update:model-value="handleRangeChange"
    >
      <SelectTrigger class="w-40">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem
          v-for="option in timeRangeOptions"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </SelectItem>
      </SelectContent>
    </Select>

    <!-- 自定义时间选择器 -->
    <Popover v-if="isCustomRange" v-model:open="showCustomPicker">
      <PopoverTrigger as-child>
        <Button variant="outline" size="sm">
          <Calendar class="w-4 h-4 mr-1" />
          选择时间
        </Button>
      </PopoverTrigger>
      <PopoverContent class="w-auto p-4" align="start">
        <div class="space-y-4">
          <div class="space-y-2">
            <Label class="text-sm font-medium">快速选择</Label>
            <div class="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                @click="setQuickRange(1)"
              >
                1小时
              </Button>
              <Button
                variant="outline"
                size="sm"
                @click="setQuickRange(24)"
              >
                24小时
              </Button>
              <Button
                variant="outline"
                size="sm"
                @click="setQuickRange(168)"
              >
                7天
              </Button>
            </div>
          </div>

          <div class="grid grid-cols-1 gap-3">
            <div class="space-y-1">
              <Label class="text-sm">开始时间</Label>
              <Input
                v-model="localStartTime"
                type="datetime-local"
                class="w-full"
                @change="handleCustomTimeChange"
              />
            </div>

            <div class="space-y-1">
              <Label class="text-sm">结束时间</Label>
              <Input
                v-model="localEndTime"
                type="datetime-local"
                class="w-full"
                @change="handleCustomTimeChange"
              />
            </div>
          </div>

          <div class="flex justify-between items-center pt-2">
            <div class="text-xs text-muted-foreground">
              <div v-if="customRangeValid && localStartTime && localEndTime">
                时间跨度: {{ formatTimestamp(new Date(localStartTime).getTime(), 'datetime') }}
                至 {{ formatTimestamp(new Date(localEndTime).getTime(), 'datetime') }}
              </div>
              <div v-else class="text-red-500">
                请选择有效的时间范围
              </div>
            </div>

            <Button
              size="sm"
              :disabled="!customRangeValid"
              @click="applyCustomRange"
            >
              应用
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>

    <!-- 当前时间范围显示 -->
    <!-- <div v-if="!isCustomRange" class="text-xs text-muted-foreground">
      {{ formatTimestamp(currentTimeRange.startTime, 'datetime') }}
      至 {{ formatTimestamp(currentTimeRange.endTime, 'datetime') }}
    </div> -->
  </div>
</template>
