<script setup lang="ts">
import {
  <PERSON>,
  Shield,
  Sun,
} from 'lucide-vue-next'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
} from '@/components/ui/sidebar'
import { menuRoutes } from '@/router'
import { useProjectStore } from '@/store/project'

const route = useRoute()
const projectStore = useProjectStore()
const sidebarOpen = ref(true)

function isMenuActive(menuPath: string, currentPath: string): boolean {
  if (menuPath === currentPath) {
    return true
  }

  // 检查是否为子路径
  if (currentPath.startsWith(menuPath) && menuPath !== '/') {
    return true
  }

  return false
}

// 初始化项目
async function initializeProjects() {
  try {
    // 确保项目列表已加载
    if (!projectStore.hasProjects) {
      await projectStore.fetchProjects()
    }

    // 如果没有当前项目，尝试初始化
    if (!projectStore.currentProject) {
      await projectStore.initializeProject()
    }
  }
  catch (error) {
    console.error('初始化项目失败:', error)
  }
}

// 组件挂载时初始化项目
onMounted(() => {
  initializeProjects()
})
</script>

<template>
  <SidebarProvider :open="sidebarOpen" @update:open="sidebarOpen = $event">
    <!-- 侧边栏 -->
    <Sidebar side="left" variant="sidebar" collapsible="offcanvas" class="theme-transition">
      <!-- 侧边栏头部 -->
      <SidebarHeader class="py-4">
        <div class="flex items-center justify-between px-4">
          <router-link to="/" class="flex items-center space-x-2">
            <Shield class="h-6 w-6 text-primary" />
            <span class="font-bold text-lg text-sidebar-foreground">Web Monitor</span>
          </router-link>
          <!-- 主题切换按钮 -->
          <Button
            variant="ghost"
            size="icon"
            class="h-10 w-10 text-sidebar-foreground hover:bg-sidebar-accent cursor-pointer"
            @click="() => {}"
          >
            <Sun v-if="true" class="h-4 w-4" />
            <Moon v-else class="h-4 w-4" />
          </Button>
        </div>
      </SidebarHeader>

      <Separator />

      <!-- 导航菜单 -->
      <SidebarContent class="px-2 py-4">
        <SidebarMenu>
          <SidebarMenuItem
            v-for="item in menuRoutes" :key="item.path"
          >
            <SidebarMenuButton
              class="h-fit py-0 theme-transition"
              :is-active="isMenuActive(item.path, route.path)"
            >
              <RouterLink :to="item.path" class="w-full">
                <div class="flex items-center gap-2 px-4 py-2.5">
                  <component :is="item.meta?.icon" class="h-4 w-4" />
                  <span>{{ item.meta?.title }}</span>
                </div>
              </RouterLink>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>

      <!-- 用户信息区域 -->
      <SidebarFooter>
        <Separator />
      </SidebarFooter>
    </Sidebar>
    <!-- 主要内容区域 -->
    <SidebarInset class="theme-transition">
      <!-- 主要内容 -->
      <main class="p-6">
        <router-view />
      </main>
    </SidebarInset>
  </SidebarProvider>
</template>
